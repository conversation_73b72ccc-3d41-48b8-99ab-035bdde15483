package public

import (
	"vpn-shop/backend-go/handlers/announcement"
	"vpn-shop/backend-go/handlers/public"
	"vpn-shop/backend-go/handlers/stats"

	"github.com/labstack/echo/v4"
)

// SetupPublicRoutes configures the public routes.
func SetupPublicRoutes(e *echo.Group) {
	// Transaction routes
	e.GET("/transactions", public.GetPublicTransactions)

	// Announcement routes
	e.GET("/announcements", announcement.GetAllAnnouncements)
	e.GET("/announcements/:id", announcement.GetAnnouncementByID)

	// Stats routes
	e.GET("/stats/top-servers", stats.GetTopPurchaseServers)
}