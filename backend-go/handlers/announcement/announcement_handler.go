package announcement

import (
	"net/http"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/announcement"
	"vpn-shop/backend-go/models/shared"

	"github.com/labstack/echo/v4"
)

// toAnnouncementResponse converts an Announcement model to an AnnouncementResponse DTO.
func toAnnouncementResponse(ann announcement.Announcement) announcement.AnnouncementResponse {
	return announcement.AnnouncementResponse{
		ID:        ann.ID,
		Judul:     ann.Judul,
		Isi:       ann.Isi,
		CreatedAt: ann.<PERSON>At,
		UpdatedAt: ann.UpdatedAt,
	}
}

// GetAllAnnouncements mengambil semua pengumuman.
// @Summary Get All Announcements
// @Description Mengambil daftar semua pengumuman yang tersedia.
// @Tags Public
// @Produce  json
// @Success 200 {array} announcement.AnnouncementResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /announcements [get]
func GetAllAnnouncements(c echo.Context) error {
	var announcements []announcement.Announcement
	if err := db.DB.Order("created_at desc").Find(&announcements).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil pengumuman"})
	}

	var responses []announcement.AnnouncementResponse
	for _, ann := range announcements {
		responses = append(responses, toAnnouncementResponse(ann))
	}

	return c.JSON(http.StatusOK, responses)
}

// GetAnnouncementByID mengambil satu pengumuman berdasarkan ID.
// @Summary Get Announcement By ID
// @Description Mengambil detail satu pengumuman berdasarkan ID-nya.
// @Tags Public
// @Produce  json
// @Param    id   path   int  true  "Announcement ID"
// @Success 200 {object} announcement.AnnouncementResponse
// @Failure 404 {object} shared.ErrorResponse
// @Router /announcements/{id} [get]
func GetAnnouncementByID(c echo.Context) error {
	id := c.Param("id")
	var ann announcement.Announcement
	if err := db.DB.First(&ann, id).Error; err != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengumuman tidak ditemukan"})
	}
	return c.JSON(http.StatusOK, toAnnouncementResponse(ann))
}
