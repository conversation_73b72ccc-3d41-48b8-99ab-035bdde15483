package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"vpn-shop/backend-go/api/account"
	"vpn-shop/backend-go/api/admin"
	"vpn-shop/backend-go/api/auth"
	"vpn-shop/backend-go/api/payment"
	"vpn-shop/backend-go/api/public"
	"vpn-shop/backend-go/api/purchase"
	"vpn-shop/backend-go/api/server"
	"vpn-shop/backend-go/api/user"

	"vpn-shop/backend-go/core"
	"vpn-shop/backend-go/db"
	_ "vpn-shop/backend-go/docs"
	"vpn-shop/backend-go/tasks"
	"vpn-shop/backend-go/utils"

	"github.com/go-co-op/gocron/v2"
	"github.com/golang-jwt/jwt/v5"
	"github.com/joho/godotenv"
	echojwt "github.com/labstack/echo-jwt/v4"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	l "github.com/labstack/gommon/log"
	echoSwagger "github.com/swaggo/echo-swagger"
	"gorm.io/gorm"
)

var (
	_ gorm.DeletedAt
)

// @title VPN Shop API
// @version 1.0
// @description This is the API for the VPN Shop application.
// @host localhost:8000
// @BasePath /api/v1
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description "Type 'Bearer ' followed by your JWT token to authorize."

// setupLogger membuat dan mengembalikan instance logger baru yang menulis ke file tertentu.
func setupLogger(logPath string) (*log.Logger, error) {
	if logPath == "" {
		return log.Default(), nil
	}

	if err := os.MkdirAll(filepath.Dir(logPath), 0755); err != nil {
		return nil, fmt.Errorf("gagal membuat direktori log: %w", err)
	}

	file, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return nil, fmt.Errorf("gagal membuka file log %s: %w", logPath, err)
	}

	return log.New(file, "", log.LstdFlags), nil
}

// setupLogging mengonfigurasi logger utama aplikasi dan logger request Echo.
// Keduanya akan menulis ke file yang sama jika LOG_FILE_PATH diatur di .env.
func setupLogging(e *echo.Echo) {
	// Gunakan logger default (ke konsol/terminal)
	e.Use(middleware.Logger())
	log.Println("Logging diatur ke konsol (stdout)")
}

func startScheduler(dbConn *gorm.DB) {
	s, err := gocron.NewScheduler()
	if err != nil {
		log.Fatalf("Gagal membuat scheduler: %v", err)
	}

	expirationLogger, err := setupLogger(os.Getenv("CRON_LOG_EXPIRATION_LOCATION"))
	if err != nil {
		log.Fatalf("Gagal menginisialisasi logger untuk expired accounts: %v", err)
	}

	dailyMaintenanceLogger, err := setupLogger(os.Getenv("CRON_LOG_DAILY_MAINTENANCE_LOCATION"))
	if err != nil {
		log.Fatalf("Gagal menginisialisasi logger untuk pemeliharaan harian: %v", err)
	}

	hourlyBillingLogger, err := setupLogger(os.Getenv("CRON_LOG_HOURLY_BILLING_LOCATION"))
	if err != nil {
		log.Fatalf("Failed to initialize logger for hourly billing: %v", err)
	}
	tasks.SetHourlyBillingLogger(hourlyBillingLogger)

	expirationInterval, _ := strconv.Atoi(os.Getenv("ACCOUNT_EXPIRATION_MINUTES"))
	if expirationInterval <= 0 {
		expirationInterval = 1
	}
	_, err = s.NewJob(
		gocron.DurationJob(
			time.Duration(expirationInterval)*time.Minute,
		),
		gocron.NewTask(
			tasks.CheckAndExpireAccounts,
			dbConn,
			expirationLogger,
		),
	)
	if err != nil {
		log.Fatalf("Gagal membuat job pengecekan akun expired: %v", err)
	}
	expirationLogger.Printf("Scheduler dimulai. Pengecekan akun kadaluwarsa akan berjalan setiap %d menit.", expirationInterval)

	maintenanceTime := os.Getenv("DAILY_MAINTENANCE_TIME")
	if maintenanceTime == "" {
		maintenanceTime = "00:00"
	}

	timeParts := strings.Split(maintenanceTime, ":")
	hour, min := 0, 0
	if len(timeParts) == 2 {
		hour, _ = strconv.Atoi(timeParts[0])
		min, _ = strconv.Atoi(timeParts[1])
	}

	dailyJobDef := gocron.DailyJob(
		1,
		gocron.NewAtTimes(
			gocron.NewAtTime(uint(hour), uint(min), 0),
		),
	)

	// Schedule the single daily maintenance task
	_, err = s.NewJob(dailyJobDef, gocron.NewTask(tasks.RunDailyMaintenance, dbConn, dailyMaintenanceLogger))
	if err != nil {
		log.Fatalf("Gagal membuat job pemeliharaan harian: %v", err)
	}
	dailyMaintenanceLogger.Printf("Tugas pemeliharaan harian dijadwalkan setiap hari pada jam %s.", maintenanceTime)

	tasks.ScheduleHourlyBilling(s)

	s.Start()
}

func main() {
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		log.Fatalf("Failed to load location: %v", err)
	}
	time.Local = loc
	if err := godotenv.Load(); err != nil {
		log.Fatal("Error loading .env file")
	}

	if err := utils.InitCrypto(); err != nil {
		log.Fatalf("Failed to initialize crypto: %v", err)
	}

	cfg := core.LoadConfig()

	db.Init(cfg)
	db.CreateInitialData(db.DB, cfg)

	go startScheduler(db.DB)

	e := echo.New()
	e.Validator = utils.NewValidator()

	// Pindahkan setupLogging ke setelah inisialisasi Echo agar bisa mengkonfigurasi middleware logger-nya.
	setupLogging(e)

	// Set logger level to INFO to see all debug messages
	e.Logger.SetLevel(l.INFO)

	// Middleware Logger sekarang diatur di dalam setupLogging(), jadi baris di bawah ini tidak diperlukan lagi.
	// e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: cfg.BackendCORSOrigins,
		AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
	}))

	e.GET("/", func(c echo.Context) error {
		return c.String(http.StatusOK, "Welcome to the VPN Shop API!")
	})
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	apiV1 := e.Group("/api/v1")
	jwtConfig := echojwt.Config{
		NewClaimsFunc: func(c echo.Context) jwt.Claims {
			return new(jwt.MapClaims)
		},
		SigningKey: []byte(cfg.JWTSecretKey),
	}
	authMiddleware := echojwt.WithConfig(jwtConfig)

	// Middleware untuk membersihkan token dari spasi dan tanda kutip ganda
	jwtTokenCleanerMiddleware := func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authHeader := c.Request().Header.Get("Authorization")
			if strings.HasPrefix(authHeader, "Bearer ") {
				// Membersihkan token dari "Bearer " dan tanda kutip
				token := strings.TrimPrefix(authHeader, "Bearer ")
				token = strings.TrimSpace(token)
				token = strings.Trim(token, "\"") // Perbaikan: Menghapus hanya karakter kutipan ganda
				// Set header kembali dengan format yang benar
				c.Request().Header.Set("Authorization", "Bearer "+token)
			}
			return next(c)
		}
	}

	auth.SetupAuthRoutes(apiV1)
	// Public callback route from Tripay, no JWT auth needed.
	apiV1.POST("/payment/callback", payment.TripayCallbackHandler)

	// Public routes
	public.SetupPublicRoutes(apiV1)

	// Authenticated payment routes
	paymentGroup := apiV1.Group("/payment")
	paymentGroup.Use(jwtTokenCleanerMiddleware) // Jalankan pembersih token terlebih dahulu
	paymentGroup.Use(authMiddleware)
	payment.SetupPaymentRoutes(paymentGroup)

	adminGroup := apiV1.Group("/admin")
	adminGroup.Use(authMiddleware)
	admin.SetupAdminRoutes(adminGroup)

	usersGroup := apiV1.Group("/users")
	usersGroup.Use(authMiddleware)
	user.SetupUserRoutes(usersGroup)

	serversGroup := apiV1.Group("/servers")
	serversGroup.Use(authMiddleware)
	server.SetupServerRoutes(serversGroup)

	purchaseGroup := apiV1.Group("/purchase")
	purchaseGroup.Use(authMiddleware)
	purchase.SetupPurchaseRoutes(purchaseGroup)

	accountGroup := apiV1.Group("/accounts")
	accountGroup.Use(authMiddleware)
	account.SetupAccountRoutes(accountGroup)

	serverAddr := fmt.Sprintf("%s:%s", cfg.ServerHost, cfg.ServerPort)
	fmt.Printf("Server is running on http://%s\n", serverAddr)
	fmt.Printf("Swagger docs available at http://%s/swagger/index.html\n", serverAddr)

	if err := e.Start(serverAddr); err != nil {
		e.Logger.Fatal(err)
	}
}
