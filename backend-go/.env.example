PROJECT_NAME=VPN Shop
SERVER_HOST=localhost
SERVER_PORT=8000
# --- Database Configuration ---
DB_HOST=localhost
DB_PORT=5433
DB_USER=vpnshop_user
DB_PASSWORD=vpnshop_password
DB_NAME=vpnshop_db
JWT_SECRET_KEY=

# Telegram Bot Secret Key (must match the one in the bot)
TELEGRAM_BOT_SECRET_AUTH=c36587067bd9be3d3d231f94194984a9f473e749e3423c88c5be482638d5c342

# Diper<PERSON>an untuk validasi Telegram Login Widget
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=90
FIRST_SUPERUSER_USERNAME=admin
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_NAME=Bobby Unknown
FIRST_SUPERUSER_PASSWORD=admin
# Daftar origin yang diizinkan untuk CORS, dipisahkan oleh koma.
# Contoh: http://localhost:3000,https://app.yourdomain.com
BACKEND_CORS_ORIGINS=http://localhost:3000
ENCRYPTION_KEY=1924a20bc2eb3529bc31d6a8d78854ab
ACCOUNT_EXPIRATION_MINUTES=90
CRON_LOG_EXPIRATION_LOCATION=/home/<USER>/Project/vpn-shop/backend-go/logs/expired.log

# Waktu (format HH:MM) untuk menjalankan reset trial harian
DAILY_MAINTENANCE_TIME="00:00"
# Lokasi file log untuk semua tugas pemeliharaan harian
CRON_LOG_DAILY_MAINTENANCE_LOCATION="./logs/cron/daily_maintenance.log"

# Lokasi file log untuk penagihan per jam
CRON_LOG_HOURLY_BILLING_LOCATION=/home/<USER>/Project/vpn-shop/backend-go/logs/hourly_billing.log
CRON_LOG_HOURLY_BILLING_CSV_DIR=/home/<USER>/Project/vpn-shop/backend-go/logs/billing_records

# Interval pengecekan penagihan per jam dalam menit
HOURLY_BILLING_INTERVAL_MINUTES=1
MINIMAL_SALDO=5000
MINIMAL_TOPUP=10000
MINIMAL_TOPUP_RESELLER=10000



#TRIPAY CONFIG
TRIPAY_PRIVATE_KEY=Se7Up-VJEaB-w10dZ-YjOIt-BlEy2
TRIPAY_API_KEY=DEV-nXInvcxaX3N4sBaeD1jSvEbr9hfjs7sKoDTK0DAy
TRIPAY_MERCHANT_CODE=T35459
TRIPAY_BASE_URL=https://tripay.co.id/api-sandbox
# URL untuk redirect setelah top-up berhasil (ke halaman invoice)
TRIPAY_RETURN_URL=http://localhost:3000/product/order/invoice
# URL untuk redirect setelah purchase/trial/renewal berhasil (ke halaman account)
TRIPAY_ACCOUNT_RETURN_URL=http://localhost:3000/account
# Path untuk file log utama aplikasi. Jika kosong, log akan ditampilkan di konsol.
APP_LOG_FILE_PATH="./logs/golang.log"

# URL ini akan digunakan oleh Tripay untuk mengirim notifikasi callback.
# Pastikan ini adalah URL publik yang stabil (misalnya dari Cloudflare Tunnel).
TRIPAY_CALLBACK_URL=https://go.insomvpn.store/api/v1/payment/callback