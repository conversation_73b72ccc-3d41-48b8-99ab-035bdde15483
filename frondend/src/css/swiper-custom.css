/* Custom Swiper Styles */

/* Container styling */
.server-slideshow-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Ensure navigation buttons are visible */
.server-slideshow .swiper-button-next,
.server-slideshow .swiper-button-prev {
  color: #ffffff;
  background: rgba(0, 0, 0, 0.3);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  margin-top: -20px; /* Center vertically */
}

.server-slideshow .swiper-button-next:hover,
.server-slideshow .swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.5);
}

.server-slideshow .swiper-button-next:after,
.server-slideshow .swiper-button-prev:after {
  font-size: 18px;
  font-weight: bold;
}

/* Pagination bullets */
.server-slideshow .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 0.7;
}

.server-slideshow .swiper-pagination-bullet-active {
  background: #ffffff;
  opacity: 1;
}

/* Dark mode adjustments */
.dark .server-slideshow .swiper-button-next,
.dark .server-slideshow .swiper-button-prev {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
}

.dark .server-slideshow .swiper-button-next:hover,
.dark .server-slideshow .swiper-button-prev:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Add navigation elements to DOM if they don't exist */
.server-slideshow {
  position: relative;
}

.server-slideshow .swiper-button-next,
.server-slideshow .swiper-button-prev,
.server-slideshow .swiper-pagination {
  position: absolute;
  z-index: 10;
}

/* Fix for pagination positioning */
.server-slideshow .swiper-pagination {
  bottom: 0 !important;
}

/* Ensure buttons are visible on top of content */
.server-slideshow .swiper-button-next {
  right: 10px;
}

.server-slideshow .swiper-button-prev {
  left: 10px;
}