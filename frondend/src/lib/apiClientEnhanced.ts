/**
 * Enhanced API Client dengan fitur tambahan untuk mengurangi pengulangan kode
 * dan meningkatkan maintainability
 */

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getToken } from 'next-auth/jwt';
import { NextRequest } from 'next/server';

// ===== TYPES =====
interface ApiClientOptions extends RequestInit {
  token?: string;
  skipAuth?: boolean;
  timeout?: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: number;
}

// ===== ENHANCED API CLIENT =====
export class ApiClient {
  private baseUrl: string;
  private defaultTimeout: number;

  constructor(baseUrl?: string, timeout = 10000) {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_API_URL || '';
    this.defaultTimeout = timeout;
  }

  public async makeRequest<T = any>(
    path: string, 
    options: ApiClientOptions = {}
  ): Promise<T> {
    const { token, skipAuth, timeout, ...restOptions } = options;

    const headers = new Headers(restOptions.headers || {});

    // Set Content-Type jika belum ada
    if (!headers.has('Content-Type') && restOptions.method !== 'GET') {
      headers.set('Content-Type', 'application/json');
    }

    // Tambahkan token otentikasi jika tersedia dan tidak di-skip
    if (token && !skipAuth) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    // Setup timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout || this.defaultTimeout);

    try {
      const response = await fetch(`${this.baseUrl}${path}`, {
        ...restOptions,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        const error = new Error(data.error || data.message || `HTTP ${response.status}: ${response.statusText}`) as any;
        error.status = response.status;
        error.details = data.details || null;
        error.response = { data, status: response.status };
        throw error;
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  // ===== HTTP METHODS =====
  async get<T = any>(path: string, options: Omit<ApiClientOptions, 'method'> = {}): Promise<T> {
    return this.makeRequest<T>(path, { ...options, method: 'GET' });
  }

  async post<T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.makeRequest<T>(path, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.makeRequest<T>(path, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(path: string, options: Omit<ApiClientOptions, 'method'> = {}): Promise<T> {
    return this.makeRequest<T>(path, { ...options, method: 'DELETE' });
  }

  async patch<T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.makeRequest<T>(path, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// ===== SINGLETON INSTANCE =====
export const apiClient = new ApiClient();

// ===== HELPER FUNCTIONS =====

// Untuk Server Components
export async function createAuthenticatedApiClient(): Promise<ApiClient> {
  const session = await getServerSession(authOptions);
  
  if (!session?.accessToken) {
    throw new Error('Tidak terautentikasi atau token tidak ada.');
  }

  const client = new ApiClient();
  const token = session.accessToken as string;

  // Override methods dengan token yang sudah terikat
  client.get = <T = any>(path: string, options: Omit<ApiClientOptions, 'method' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'GET', token });
  
  client.post = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'POST', body: data ? JSON.stringify(data) : undefined, token });
  
  client.put = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'PUT', body: data ? JSON.stringify(data) : undefined, token });
  
  client.delete = <T = any>(path: string, options: Omit<ApiClientOptions, 'method' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'DELETE', token });
  
  client.patch = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'PATCH', body: data ? JSON.stringify(data) : undefined, token });

  return client;
}

// Untuk API Routes
export async function createApiClientFromRequest(req: NextRequest): Promise<ApiClient> {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  
  if (!token?.accessToken) {
    throw new Error('Tidak terautentikasi atau token tidak ada.');
  }

  const client = new ApiClient();
  const accessToken = token.accessToken as string;

  // Override methods dengan token yang sudah terikat
  client.get = <T = any>(path: string, options: Omit<ApiClientOptions, 'method' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'GET', token: accessToken });
  
  client.post = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'POST', body: data ? JSON.stringify(data) : undefined, token: accessToken });
  
  client.put = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'PUT', body: data ? JSON.stringify(data) : undefined, token: accessToken });
  
  client.delete = <T = any>(path: string, options: Omit<ApiClientOptions, 'method' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'DELETE', token: accessToken });
  
  client.patch = <T = any>(path: string, data?: any, options: Omit<ApiClientOptions, 'method' | 'body' | 'token'> = {}) => 
    client.makeRequest<T>(path, { ...options, method: 'PATCH', body: data ? JSON.stringify(data) : undefined, token: accessToken });

  return client;
}

// Error handling wrapper
export async function safeApiCall<T>(
  apiCall: () => Promise<T>, 
  fallback?: T,
  onError?: (error: Error) => void
): Promise<T | null> {
  try {
    return await apiCall();
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    console.error('API call failed:', err.message);
    
    if (onError) {
      onError(err);
    }
    
    return fallback || null;
  }
}

// ===== BACKWARD COMPATIBILITY =====
// Untuk menjaga kompatibilitas dengan apiClient.ts yang sudah ada
export async function apiClientLegacy(path: string, options: ApiClientOptions = {}) {
  return apiClient.makeRequest(path, options);
}