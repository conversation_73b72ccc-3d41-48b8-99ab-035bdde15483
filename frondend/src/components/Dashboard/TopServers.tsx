'use client';

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { ServerIcon } from '@/assets/icons'; // Placeholder icon

// Definisikan tipe data untuk server terpopuler
interface TopServer {
  server_kode: string;
  server_name: string;
  server_country: string;
  slot_server: number;
  slot_terpakai: number;
  total_user: number;
}

// Fungsi untuk mengambil data server terpopuler
async function getTopServers(): Promise<TopServer[]> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${apiUrl}/stats/top-servers`);
    if (!response.ok) {
      console.error('Gagal mengambil data server terpopuler, status:', response.status);
      return [];
    }
    const data = await response.json();
    return data || [];
  } catch (error) {
    console.error('Error saat fetching top servers:', error);
    return [];
  }
}

// Fungsi untuk mendapatkan emoji bendera berdasarkan nama negara (contoh sederhana)
function getCountryFlag(country: string): string {
  switch (country.toLowerCase()) {
    case 'singapore':
      return '🇸🇬';
    case 'indonesia':
      return '🇮🇩';
    case 'united states':
      return '🇺🇸';
    default:
      return '🏳️'; // Bendera putih sebagai default
  }
}

// Fungsi untuk mendapatkan medali berdasarkan peringkat
function getMedal(rank: number): string {
  if (rank === 0) return '🥇'; // Emas
  if (rank === 1) return '🥈'; // Perak
  if (rank === 2) return '🥉'; // Perunggu
  return `${rank + 1}`;
}

export function TopServers({ className }: { className?: string }) {
  const [topServers, setTopServers] = useState<TopServer[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadTopServers() {
      setLoading(true);
      const data = await getTopServers();
      setTopServers(data);
      setLoading(false);
    }
    loadTopServers();
  }, []);

  

  return (
    <div
      className={cn(
        "grid gap-4 rounded-xl bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex items-center gap-2">
        <ServerIcon className="h-6 w-6 text-dark dark:text-white" />
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Server Terpopuler
        </h2>
      </div>

      <div className="h-[350px] overflow-y-auto pr-2 custom-scrollbar">
        {loading ? (
          <div className="flex h-full items-center justify-center">
            <div className="h-10 w-10 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent"></div>
          </div>
        ) : topServers.length > 0 ? (
          <ul className="space-y-4">
            {topServers.map((server, index) => {
              const slotPercentage = server.slot_server > 0 ? (server.slot_terpakai / server.slot_server) * 100 : 0;
              return (
                <li 
                  key={server.server_kode} 
                  className="group flex items-center gap-4 transition-transform duration-200 ease-in-out hover:-translate-y-1"
                >
                  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-light text-lg font-bold text-dark dark:bg-gray-dark/50 dark:text-white">
                    {getMedal(index)}
                  </span>
                  <p className="flex-1 truncate font-semibold text-dark dark:text-white" title={server.server_name}>
                      {getCountryFlag(server.server_country)} {server.server_name}
                    </p>
                    <div className="relative w-1/3">
                      <div className="h-4 w-full rounded-full bg-gray-light dark:bg-gray-dark/50"></div>
                      <div 
                        className="absolute top-0 left-0 flex h-full items-center justify-center rounded-full bg-primary"
                        style={{ width: `${slotPercentage}%` }}
                      >
                        <span className="text-[10px] font-bold text-white">
                          {`${Math.round(slotPercentage)}%`}
                        </span>
                      </div>
                    </div>
                    <div className="w-24 text-right">
                      <p className="font-semibold text-dark dark:text-white">
                        {server.slot_terpakai} / {server.slot_server}
                      </p>
                    </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <div className="flex h-full flex-col items-center justify-center text-center text-gray-500 dark:text-dark-6">
            <p className="text-lg font-medium">Data Kosong</p>
            <p className="text-sm">Belum ada data server yang tersedia.</p>
          </div>
        )}
      </div>
    </div>
  );
}
