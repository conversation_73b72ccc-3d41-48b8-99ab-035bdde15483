"use client";

import {useEffect, useState} from "react";
import Image from "next/image";
import {formatDistanceToNow} from "date-fns";
import {id} from 'date-fns/locale';
import {standardFormat} from "@/lib/format-number";
import {PublicTransaction} from "@/types/transaction";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const TransactionTable = ({data}: { data: PublicTransaction[] }) => {
  if (data.length === 0) {
    return <p className="text-center py-10 text-body-color">Tidak ada data transaksi untuk kategori ini.</p>;
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="border-none uppercase">
            <TableHead className="min-w-[200px] !pl-0">User</TableHead>
            <TableHead>Deskripsi</TableHead>
            <TableHead className="text-right"><PERSON><PERSON>lah</TableHead>
            <TableHead className="text-right">Waktu</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((tx) => (
            <TableRow key={tx.id} className="text-base font-medium text-dark dark:text-white">
              <TableCell className="flex items-center gap-3 !pl-0">
                <Image
                  src="/images/user/user-01.png"
                  className="size-8 rounded-full object-cover"
                  width={40}
                  height={40}
                  alt={tx.user_name}
                />
                <span>{tx.user_name}</span>
              </TableCell>
              <TableCell>{tx.description}</TableCell>
              <TableCell className="text-right text-green-light-1">Rp {standardFormat(tx.amount)}</TableCell>
              <TableCell className="text-right">
                {formatDistanceToNow(new Date(tx.created_at), {addSuffix: true, locale: id})}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

const Transactions = () => {
  const [activeTab, setActiveTab] = useState<'topup' | 'purchase' | 'trial'>('topup');
  const [data, setData] = useState<{
    topup: PublicTransaction[];
    purchase: PublicTransaction[];
    trial: PublicTransaction[];
  }>({topup: [], purchase: [], trial: []});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/transactions?type=${activeTab}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || result.details || 'Gagal mengambil data transaksi');
      }

      if (!Array.isArray(result)) {
        throw new Error('Format data tidak sesuai');
      }

      setData(prev => ({...prev, [activeTab]: result}));
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const renderContent = () => {
    if (loading) {
      return <div className="text-center py-10">Memuat data...</div>;
    }

    if (error) {
      return (
        <div className="text-center py-10 text-red-500">
          {error}
          <button
            onClick={fetchData}
            className="block mx-auto mt-4 text-primary hover:underline"
          >
            Coba lagi
          </button>
        </div>
      );
    }

    return <TransactionTable data={data[activeTab]}/>
  };

  return (
    <div className="rounded-[10px] bg-white px-7.5 pb-4 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card">
      <h2 className="mb-4 text-body-2xlg font-bold text-dark dark:text-white">
        Transaksi Terbaru
      </h2>

      <div className="mb-7.5 flex flex-wrap gap-10 border-b border-stroke dark:border-dark-3">
        <button
          onClick={() => setActiveTab('topup')}
          data-active={activeTab === 'topup'}
          className="border-b-2 border-transparent pb-[7px] font-medium hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary"
        >
          Top Up
        </button>
        <button
          onClick={() => setActiveTab('purchase')}
          data-active={activeTab === 'purchase'}
          className="border-b-2 border-transparent pb-[7px] font-medium hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary"
        >
          Pembelian
        </button>
        <button
          onClick={() => setActiveTab('trial')}
          data-active={activeTab === 'trial'}
          className="border-b-2 border-transparent pb-[7px] font-medium hover:border-primary hover:text-primary data-[active=true]:border-primary data-[active=true]:text-primary"
        >
          Trial
        </button>
      </div>

      <div>
        {renderContent()}
      </div>
    </div>
  );
};

export default Transactions;