"use client";

import {useEffect, useState} from "react";
import Image from "next/image";
import {formatDistanceToNow} from "date-fns";
import {id} from 'date-fns/locale';
import {standardFormat} from "@/lib/format-number";
import {PublicTransaction} from "@/types/transaction";
// Removed unused table imports

const TransactionCard = ({transaction}: { transaction: PublicTransaction }) => {
  const getTransactionIcon = (description: string) => {
    if (description.toLowerCase().includes('top up')) return '💰';
    if (description.toLowerCase().includes('trial')) return '🎯';
    if (description.toLowerCase().includes('purchase') || description.toLowerCase().includes('pembelian')) return '🛒';
    return '💳';
  };

  const getAmountColor = (amount: number) => {
    if (amount > 0) return 'text-emerald-500';
    return 'text-red-500';
  };

  return (
    <div className="group relative overflow-hidden rounded-xl border border-stroke bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-lg hover:border-primary/30 hover:-translate-y-1 dark:border-dark-3 dark:bg-gray-dark">
      {/* Gradient Border Effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>

      <div className="relative z-10 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* User Avatar dengan Border Gradient */}
          <div className="relative">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-purple-500 p-0.5 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              <div className="h-full w-full rounded-full bg-white dark:bg-gray-dark"></div>
            </div>
            <Image
              src="/images/user/user-01.png"
              className="relative z-10 h-12 w-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 transition-all duration-300 group-hover:border-transparent"
              width={48}
              height={48}
              alt={transaction.user_name}
            />
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="text-lg transition-transform duration-300 group-hover:scale-110">
                {getTransactionIcon(transaction.description)}
              </span>
              <h4 className="font-semibold text-dark dark:text-white transition-colors duration-300 group-hover:text-primary">
                {transaction.user_name}
              </h4>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {transaction.description}
            </p>
          </div>
        </div>

        <div className="text-right space-y-1">
          <div className={`text-lg font-bold transition-all duration-300 group-hover:scale-105 ${getAmountColor(transaction.amount)}`}>
            Rp {standardFormat(transaction.amount)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {formatDistanceToNow(new Date(transaction.created_at), {
              addSuffix: true,
              locale: id,
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

const TransactionList = ({data}: { data: PublicTransaction[] }) => {
  if (data.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="text-6xl mb-4 animate-bounce">📭</div>
        <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
          Belum ada transaksi
        </h3>
        <p className="text-gray-500 dark:text-gray-500">
          Transaksi akan muncul di sini setelah ada aktivitas
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {data.map((tx) => (
        <TransactionCard key={tx.id} transaction={tx} />
      ))}
    </div>
  );
};

const Transactions = () => {
  const [activeTab, setActiveTab] = useState<'topup' | 'purchase' | 'trial'>('topup');
  const [data, setData] = useState<{
    topup: PublicTransaction[];
    purchase: PublicTransaction[];
    trial: PublicTransaction[];
  }>({topup: [], purchase: [], trial: []});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/transactions?type=${activeTab}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || result.details || 'Gagal mengambil data transaksi');
      }

      if (!Array.isArray(result)) {
        throw new Error('Format data tidak sesuai');
      }

      setData(prev => ({...prev, [activeTab]: result}));
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const renderContent = () => {
    if (loading) {
      return (
        <div className="text-center py-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 bg-primary/10 rounded-full">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary border-t-transparent"></div>
            <span className="text-primary font-medium">Memuat data transaksi...</span>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-16">
          <div className="text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-600 dark:text-red-400 mb-2">
            Terjadi Kesalahan
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={fetchData}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Coba Lagi
          </button>
        </div>
      );
    }

    return <TransactionList data={data[activeTab]}/>
  };

  const tabs = [
    { id: 'topup', label: 'Top Up', icon: '💰', color: 'from-emerald-500 to-green-600' },
    { id: 'purchase', label: 'Pembelian', icon: '🛒', color: 'from-blue-500 to-indigo-600' },
    { id: 'trial', label: 'Trial', icon: '🎯', color: 'from-purple-500 to-pink-600' },
  ];

  return (
    <div className="space-y-6">
      {/* Modern Header dengan Gradient */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary via-blue-600 to-purple-600 p-6 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -right-8 -top-8 h-32 w-32 rounded-full bg-white/10 backdrop-blur-sm"></div>
        <div className="absolute -left-4 -bottom-4 h-24 w-24 rounded-full bg-white/5 backdrop-blur-sm"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
              </svg>
            </div>
            <h2 className="text-2xl font-bold">Transaksi Terbaru</h2>
          </div>
          <p className="text-white/80">Pantau aktivitas transaksi real-time dari semua pengguna</p>
        </div>
      </div>

      {/* Modern Tab Navigation */}
      <div className="flex flex-wrap gap-2 p-1.5 bg-gray-100 dark:bg-gray-800 rounded-xl">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`
              relative flex items-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-300 overflow-hidden
              ${activeTab === tab.id
                ? 'bg-white dark:bg-gray-700 text-primary shadow-lg transform scale-105'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-700/50'
              }
            `}
          >
            {activeTab === tab.id && (
              <div className={`absolute inset-0 bg-gradient-to-r ${tab.color} opacity-10`}></div>
            )}
            <span className="text-lg relative z-10">{tab.icon}</span>
            <span className="text-sm font-semibold relative z-10">{tab.label}</span>
            {activeTab === tab.id && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-purple-500"></div>
            )}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="rounded-xl bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 p-6 shadow-sm">
        {renderContent()}
      </div>
    </div>
  );
};

export default Transactions;