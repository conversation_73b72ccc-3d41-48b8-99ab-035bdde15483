'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useNotification } from '@/contexts/NotificationContext';
import { ApiClient } from '@/lib/apiClientEnhanced';
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { getFlagEmoji } from '@/utils/country';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import '@/css/swiper-custom.css';
import '@/css/badge.css';

interface Server {
  server_id: number;
  nama: string;
  negara: string;
  nama_isp: string;
  harga_member: number;
  trojan: string;
  vmess: string;
  vless: string;
}

// Ikon Mata untuk badge protokol
const EyeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5C17 19.5 21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z"
    />
  </svg>
);

const ServiceButton = ({ name, serverId, active }: { name: string; serverId: number; active: boolean }) => {
  if (!active) return null;
  const servicePath = name.toLowerCase();
  
  // Menentukan warna badge berdasarkan protokol
  let badgeClass = '';
  if (name === 'Vmess') {
    badgeClass = 'badge-pill badge-pill-primary';
  } else if (name === 'Vless') {
    badgeClass = 'badge-pill badge-pill-success';
  } else if (name === 'Trojan') {
    badgeClass = 'badge-pill badge-pill-danger';
  }
  
  return (
    <Link
      href={`/product/${servicePath}/${serverId}`}
      className={`${badgeClass} text-xs font-medium transition-colors hover:shadow-md mx-2 flex items-center justify-center min-w-[60px] px-2 py-[2px]`}
    >
      {name}
      <EyeIcon className="ml-1" />
    </Link>
  );
};

const ServerCard = ({ server }: { server: Server }) => (
  <div className="relative flex w-full flex-col overflow-hidden rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white shadow-lg md:flex-row md:items-center">
    <div className="flex-shrink-0 text-center md:text-left">
      <div className="text-6xl">{getFlagEmoji(server.negara)}</div>
      <h3 className="mt-2 text-2xl font-bold">{server.nama}</h3>
      <p className="opacity-80">{server.nama_isp}</p>
    </div>
    <div className="my-4 w-full border-t border-white/20 md:my-0 md:mx-6 md:h-24 md:w-px md:border-l"></div>
    <div className="flex flex-grow flex-col items-center justify-center text-center">
      <p className="text-lg opacity-90">Mulai dari</p>
      <p className="my-1 text-4xl font-extrabold">
        Rp{server.harga_member.toLocaleString('id-ID')}
        <span className="text-base font-medium opacity-70">/bulan</span>
      </p>
      <div className="mt-3 flex flex-wrap justify-center gap-3">
        <ServiceButton name="Vmess" serverId={server.server_id} active={server.vmess.startsWith('enable')} />
        <ServiceButton name="Vless" serverId={server.server_id} active={server.vless.startsWith('enable')} />
        <ServiceButton name="Trojan" serverId={server.server_id} active={server.trojan.startsWith('enable')} />
      </div>
    </div>
  </div>
);

const ServerSlideshow = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();
  const { addNotification } = useNotification();

  useEffect(() => {
    const fetchServers = async () => {
      if (!session?.accessToken) {
        setLoading(false);
        return;
      }
      try {
        const apiClient = new ApiClient();
        const responseData = await apiClient.get('/servers', { token: session.accessToken });
        const serverList: Server[] = responseData.servers || [];

        if (!Array.isArray(serverList)) {
          console.error('Format data server dari API tidak valid. Proses dihentikan.', serverList);
          // Menghentikan eksekusi secara diam-diam untuk mencegah error loop notifikasi.
          return;
        }

        const activeServers = serverList.filter(s => s.vmess.startsWith('enable') || s.vless.startsWith('enable') || s.trojan.startsWith('enable'));
        setServers(activeServers);
      } catch (error) {
        console.error('Error fetching servers:', error);
        addNotification('Gagal memuat daftar server. Silakan coba lagi.', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, [session, addNotification]);

  if (loading) {
    return (
      <div className="col-span-12 flex h-48 items-center justify-center rounded-[10px] bg-white px-7.5 py-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
        <p className="text-black dark:text-white">Memuat Server Pilihan...</p>
      </div>
    );
  }

  if (servers.length === 0) {
    return null;
  }

  return (
    <div className="col-span-12 rounded-[10px] bg-white py-6 shadow-1 dark:bg-gray-dark dark:shadow-card">
      <div className="flex justify-between items-center mb-5 px-7.5">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          Server Pilihan Untuk Anda
        </h4>
      </div>

      {/* Container dengan tombol navigasi di luar */}
      <div className="relative flex items-center">
        {/* Tombol Previous - Kiri */}
        <button className="external-nav-prev z-10 flex-shrink-0 ml-4 mr-2 p-2 rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200 group">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300 group-hover:text-gray-800 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        {/* Slideshow Container */}
        <div className="flex-1 px-2">
          <div className="server-slideshow-container">
            <Swiper
              modules={[Navigation, Pagination, Autoplay]}
              spaceBetween={30}
              slidesPerView={1}
              navigation={{
                nextEl: '.external-nav-next',
                prevEl: '.external-nav-prev',
              }}
              pagination={{
                clickable: true,
                dynamicBullets: true,
                el: '.swiper-pagination'
              }}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true
              }}
              className="server-slideshow !pb-12"
            >
              {servers.map(server => (
                <SwiperSlide key={server.server_id}>
                  <ServerCard server={server} />
                </SwiperSlide>
              ))}
              {/* Pagination */}
              <div className="swiper-pagination"></div>
            </Swiper>
          </div>
        </div>

        {/* Tombol Next - Kanan */}
        <button className="external-nav-next z-10 flex-shrink-0 ml-2 mr-4 p-2 rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200 group">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300 group-hover:text-gray-800 dark:group-hover:text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ServerSlideshow;
