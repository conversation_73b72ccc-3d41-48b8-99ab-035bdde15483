"use client";

import { Sidebar } from "@/components/Layouts/sidebar";
import { BalanceProvider } from '@/contexts/BalanceContext';
import { Header } from "@/components/Layouts/header";
import Footer from '@/components/Layouts/footer';
import type { PropsWithChildren } from "react";

export default function MainPagesLayout({ children }: PropsWithChildren) {
  return (
    <BalanceProvider>
      <div className="flex min-h-screen">
        <Sidebar />
        {/* <!-- ===== Content Area Start ===== --> */}
        <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden bg-whiter dark:bg-boxdark-2">
          {/* <!-- ===== Header Start ===== --> */}
          <Header />
          {/* <!-- ===== Header End ===== --> */}

          {/* <!-- ===== Main Content Start ===== --> */}
          <main>
            <div className="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
              {children}
            </div>
          </main>
          {/* <!-- ===== Main Content End ===== --> */}

          {/* <!-- ===== Footer Start ===== --> */}
          <Footer />
          {/* <!-- ===== Footer End ===== --> */}
        </div>
        {/* <!-- ===== Content Area End ===== --> */}
      </div>
    </BalanceProvider>
  );
}
